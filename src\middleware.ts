import type { auth } from "@/lib/auth";
import { betterFetch } from "@better-fetch/fetch";
import { type NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  if (request.nextUrl.pathname.startsWith("/api/auth"))
    return NextResponse.next();
  const { data: session } = await betterFetch<typeof auth.$Infer.Session>(
    "/api/auth/get-session",
    {
      baseURL: request.nextUrl.origin,
    }
  );
  if (request.nextUrl.pathname.startsWith("/auth")) {
    if (session) 
      return NextResponse.redirect(new URL("/dashboard", request.url));
    return NextResponse.next();
  }
  if (!session) 
    return NextResponse.redirect(new URL("/auth", request.url));
  return NextResponse.next();

  // if (request.nextUrl.pathname.startsWith("/api/auth"))
  //   return NextResponse.next();
  // const session = await auth.api.getSession({ headers: await headers() });
  // if (request.nextUrl.pathname.startsWith("/sign")) {
  //   if (session) return NextResponse.redirect(new URL("/home", request.url));
  //   return NextResponse.next();
  // }
  // if (!session) return NextResponse.redirect(new URL("/sign-in", request.url));
  // return NextResponse.next();
}

export const config = {
  // runtime: "nodejs",
  matcher: ["/dashboard", "/auth", "/error"],
};