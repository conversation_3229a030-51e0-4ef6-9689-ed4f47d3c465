import type { Metada<PERSON> } from "next";
import { Pixelify_Sans } from "next/font/google";
import "@/styles/globals.css";
import ThemeProvider from "@/components/layout/theme-provider";

const pixelify = Pixelify_Sans({
  variable: "--font-pixelify-sans",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Mystique",
  description: "Generated by create next app",
};

export default ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${pixelify.variable} antialiased`}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
};
